package co.styletheory.ops.outbound.android.feature.accuracySwap.view

import android.os.Bundle
import android.os.Parcelable
import android.view.View
import android.widget.AdapterView
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import co.styletheory.ops.outbound.android.R
import co.styletheory.ops.outbound.android.databinding.ActivityAccuracySwapBinding
import co.styletheory.ops.outbound.android.feature.accuracySwap.viewModel.AccuracySwapViewModel
import co.styletheory.ops.outbound.android.general.adapter.SpinnerAdapter
import co.styletheory.ops.outbound.android.general.base.BaseActivity
import co.styletheory.ops.outbound.android.model.SwapItem
import co.styletheory.ops.outbound.android.util.EdgeToEdgeUtil
import co.styletheory.ops.outbound.android.util.IntentConstant
import co.styletheory.ops.outbound.android.util.notNull
import co.styletheory.ops.outbound.android.viewModelComponent.FooterButtonViewModel
import org.greenrobot.eventbus.Subscribe
import org.parceler.Parcels

/**
 * Created by Yoga C. Pranata on 05/05/20.
 * Android Engineer
 */
@Suppress("UNUSED_PARAMETER")
class AccuracySwapActivity : BaseActivity<ActivityAccuracySwapBinding, AccuracySwapViewModel>() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        activityComponent?.inject(this)
        bindContentView(R.layout.activity_accuracy_swap)
        setupToolbar(string(R.string.accuracy_swap_title))
        setupSwapNowButton()
        setupSwapReasonSpinner()
        initExtras()

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                navigator.finishWithResult()
            }
        })
    }

    /**
     * Custom edge-to-edge implementation for whve ite toolbar with dark text.
     * Not using super.setupEdgeToEdgeInsets() to avoid blue toolbar styling.
     */
    override fun setupEdgeToEdgeInsets() {
        // Enable edge-to-edge display
        WindowCompat.setDecorFitsSystemWindows(window, false)

        // Apply proper toolbar insets with color detection
        binding?.toolbarWrapper?.toolbar?.let { toolbar ->
            // This will detect toolbar color and set status bar accordingly
            EdgeToEdgeUtil.setupEdgeToEdgeWithToolbar(this, toolbar)
        }

        // Ensure footer button isn't hidden by navigation bar
        binding?.layoutSwapNowButton?.let { footerView ->
            EdgeToEdgeUtil.applyBottomInsets(footerView, adjustmentFactor = 1.0f)
        }

        // Apply insets to content container
        binding?.root?.let { contentRoot ->
            ViewCompat.setOnApplyWindowInsetsListener(contentRoot) { view, windowInsets ->
                val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())
                view.setPadding(insets.left, 0, insets.right, 0)
                windowInsets
            }
            ViewCompat.requestApplyInsets(contentRoot)
        }

        // Handle keyboard insets
        binding?.root?.let { rootView ->
            setupKeyboardInsets(rootView, binding?.layoutSwapNowButton)
        }
    }

    private fun setupToolbar(title: String) {
        setSupportActionBar(binding?.toolbarWrapper?.toolbar as Toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setHomeAsUpIndicator(R.drawable.ic_close)
        binding?.toolbarVM = viewModel?.toolbarViewModel()
        binding?.toolbarVM?.title?.set(title)
        binding?.toolbarWrapper?.toolbar?.setBackgroundColor(ContextCompat.getColor(this, R.color.white))
        binding?.toolbarWrapper?.toolbarTitle?.setTextColor(ContextCompat.getColor(this, R.color.very_dark_gray))
    }

    private fun setupSwapNowButton() {
        binding?.footerButtonVM = viewModel?.footerButtonViewModel()
        binding?.swapNowButton?.footerButton?.setBackgroundResource(R.drawable.selector_button_rect_green_gray_rad_5)
    }

    private fun initExtras() {
        intent.getParcelableExtra<Parcelable>(IntentConstant.SWAP_ITEM).notNull {
            val item = Parcels.unwrap<SwapItem>(it)
            viewModel?.swapItem = item
        }
    }

    private fun setupSwapReasonSpinner() {
        val reasonList = resources.getStringArray(R.array.swap_reason_list)
        val adapter = SpinnerAdapter(this, reasonList, true)
        binding?.swapReasonSpinner?.adapter = adapter
        binding?.swapReasonSpinner?.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onNothingSelected(p0: AdapterView<*>?) {
                // Do Nothing
            }

            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                viewModel?.selectedSwapReasonPosition(position)
            }

        }
    }

    @Subscribe
    fun onClickSwapNowButton(event: FooterButtonViewModel.CompleteButtonEvent) {
        navigator.withExtraResult(IntentConstant.BOTTOM_SHEET_ARGS, IntentConstant.SHOW_BOTTOMSHEET)
            .withExtraResult(IntentConstant.SWAP_ITEM, Parcels.wrap(viewModel?.swapItem))
            .finishWithResult()
    }
}